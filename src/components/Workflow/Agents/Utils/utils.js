import { message } from 'antd';
import { PROMPT_ENDPOINT,LLM_ENDPOINT } from '../../../Configs/Config';
import { fetchBulk,fetchData } from '../../../Routers/Router';

export const getLlmOptions = async (endpoint,username) => {
  try {
    const endpoint_api = `${endpoint}/bulk?username=${username}&is_published=true` ;
    const result = await fetchBulk(endpoint_api);
    return result.data;
  } catch (error) {
    message.error('Failed to fetch LLM options');
    return [];
  }
};
export const getMcpServerOptions = async (endpoint,username,mcp_type) => {
  try {
    const endpoint_api = `${endpoint}/bulk?username=${username}&mcp_type=${mcp_type}&is_published=true` ;
    const result = await fetchBulk(endpoint_api);
    console.log(result,'result')
    return result.data;
  } catch (error) {
    message.error('Failed to fetch LLM options');
    return [];
  }
};
export const getPromptOptions = async (username, agentType = null) => {
  try {
    let endpoint_api = `${PROMPT_ENDPOINT}/bulk?username=${username}&is_published=true`;
    if (agentType) {
      endpoint_api = `${endpoint_api}&agent_type=${agentType}`;
    }
    const result = await fetchBulk(endpoint_api);
    if (result.data) {
      const positivePrompts = result.data.filter(prompt => prompt.prompt_type === '正向提示词');
      const negativePrompts = result.data.filter(prompt => prompt.prompt_type === '负向提示词');
      return { positivePrompts, negativePrompts };
    }
    return { positivePrompts: [], negativePrompts: [] };
  } catch (error) {
    message.error('Failed to fetch prompt options');
    return { positivePrompts: [], negativePrompts: [] };
  }
};
export const getConstantTypesOptions = async (endpoint_api) => {
  try {
    const result = await fetchData(endpoint_api);
    const options = result.data.map(t => ({
      value: t,
      label:t
    }));
    return options
  } catch (error) {
    message.error('Failed to fetch LLM options');
    return [];
  }
};
