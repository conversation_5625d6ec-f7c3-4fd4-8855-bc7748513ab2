import React, { useState, useEffect } from 'react';
import { fetchData } from '../Routers/Router';
import { EVENT_ENDPOINT } from '../Configs/Config';

function ActionNamesDebug() {
  const [response, setResponse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchActionNames = async () => {
      try {
        setLoading(true);
        const result = await fetchData(`${EVENT_ENDPOINT}/action_names`);
        console.log('Debug - Full API response:', result);
        setResponse(result);
      } catch (err) {
        console.error('Debug - API error:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchActionNames();
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h2>Action Names API Debug</h2>
      <h3>Full Response:</h3>
      <pre style={{ background: '#f5f5f5', padding: '10px', overflow: 'auto' }}>
        {JSON.stringify(response, null, 2)}
      </pre>
      
      <h3>Data Structure Analysis:</h3>
      <ul>
        <li>Has response: {response ? 'Yes' : 'No'}</li>
        <li>Has response.data: {response?.data ? 'Yes' : 'No'}</li>
        <li>Has response.data.llm: {response?.data?.llm ? 'Yes' : 'No'}</li>
        <li>Has response.data.interruption: {response?.data?.interruption ? 'Yes' : 'No'}</li>
        <li>Has response.data.interrupt: {response?.data?.interrupt ? 'Yes' : 'No'}</li>
        <li>Is response.data an array: {Array.isArray(response?.data) ? 'Yes' : 'No'}</li>
      </ul>

      {response?.data?.llm && (
        <div>
          <h4>LLM Action Names:</h4>
          <pre>{JSON.stringify(response.data.llm, null, 2)}</pre>
        </div>
      )}

      {response?.data?.interruption && (
        <div>
          <h4>Interruption Action Names:</h4>
          <pre>{JSON.stringify(response.data.interruption, null, 2)}</pre>
        </div>
      )}

      {response?.data?.interrupt && (
        <div>
          <h4>Interrupt Action Names:</h4>
          <pre>{JSON.stringify(response.data.interrupt, null, 2)}</pre>
        </div>
      )}
    </div>
  );
}

export default ActionNamesDebug;
